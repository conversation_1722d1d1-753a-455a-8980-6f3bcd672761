QMAKE_MAC_SDK.macosx.Path = /Applications/Xcode-beta.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk
QMAKE_MAC_SDK.macosx.PlatformPath = /Applications/Xcode-beta.app/Contents/Developer/Platforms/MacOSX.platform
QMAKE_MAC_SDK.macosx.SDKVersion = 15.5
QMAKE_MAC_SDK.macx-clang.macosx.QMAKE_CC = /Applications/Xcode-beta.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang
QMAKE_MAC_SDK.macx-clang.macosx.QMAKE_CXX = /Applications/Xcode-beta.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang++
QMAKE_MAC_SDK.macx-clang.macosx.QMAKE_FIX_RPATH = \
    /Applications/Xcode-beta.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/install_name_tool \
    -id
QMAKE_MAC_SDK.macx-clang.macosx.QMAKE_AR = \
    /Applications/Xcode-beta.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/ar \
    cq
QMAKE_MAC_SDK.macx-clang.macosx.QMAKE_RANLIB = \
    /Applications/Xcode-beta.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/ranlib \
    -s
QMAKE_MAC_SDK.macx-clang.macosx.QMAKE_LINK = /Applications/Xcode-beta.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang++
QMAKE_MAC_SDK.macx-clang.macosx.QMAKE_LINK_SHLIB = /Applications/Xcode-beta.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang++
QMAKE_MAC_SDK.macx-clang.macosx.QMAKE_ACTOOL = /Applications/Xcode-beta.app/Contents/Developer/usr/bin/actool
QMAKE_MAC_SDK.macx-clang.macosx.QMAKE_LINK_C = /Applications/Xcode-beta.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang
QMAKE_MAC_SDK.macx-clang.macosx.QMAKE_LINK_C_SHLIB = /Applications/Xcode-beta.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang
QMAKE_CXX.QT_COMPILER_STDCXX = 201402L
QMAKE_CXX.QMAKE_APPLE_CC = 6000
QMAKE_CXX.QMAKE_APPLE_CLANG_MAJOR_VERSION = 17
QMAKE_CXX.QMAKE_APPLE_CLANG_MINOR_VERSION = 0
QMAKE_CXX.QMAKE_APPLE_CLANG_PATCH_VERSION = 0
QMAKE_CXX.QMAKE_GCC_MAJOR_VERSION = 4
QMAKE_CXX.QMAKE_GCC_MINOR_VERSION = 2
QMAKE_CXX.QMAKE_GCC_PATCH_VERSION = 1
QMAKE_CXX.COMPILER_MACROS = \
    QT_COMPILER_STDCXX \
    QMAKE_APPLE_CC \
    QMAKE_APPLE_CLANG_MAJOR_VERSION \
    QMAKE_APPLE_CLANG_MINOR_VERSION \
    QMAKE_APPLE_CLANG_PATCH_VERSION \
    QMAKE_GCC_MAJOR_VERSION \
    QMAKE_GCC_MINOR_VERSION \
    QMAKE_GCC_PATCH_VERSION
QMAKE_CXX.INCDIRS = \
    /Applications/Xcode-beta.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/include/c++/v1 \
    /Applications/Xcode-beta.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/17/include \
    /Applications/Xcode-beta.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/include \
    /Applications/Xcode-beta.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include
QMAKE_CXX.LIBDIRS = \
    /Applications/Xcode-beta.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib \
    /Applications/Xcode-beta.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift
QMAKE_MAC_SDK.macosx.SDKVersion = 15.5
QMAKE_MAC_SDK.macosx.Path = /Applications/Xcode-beta.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk
QMAKE_XCODE_DEVELOPER_PATH = /Applications/Xcode.app/Contents/Developer
QMAKE_XCODE_VERSION = 16.4
