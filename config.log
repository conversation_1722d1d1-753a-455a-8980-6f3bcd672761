This file contains any messages produced by compilers while
running configure, to aid debugging if configure makes a mistake.

It was created by libnoodlenet configure 1.0, which was
generated by GNU Autoconf 2.72.  Invocation command line was

  $ ./configure --prefix=/usr/local AR=/usr/local/bin/ar OBJCOPY=/usr/local/bin/llvm-objcopy LIPO=/usr/local/bin/lipo OTOOL=/usr/local/bin/otool NM=/usr/local/bin/nm LIBTOOL=/usr/local/bin/libtool STRIP=/usr/local/bin/strip DWP=/usr/local/bin/dwp OBJDUMP=/usr/local/bin/objdump ADDR2LINE=/usr/local/bin/addr2line WINDRES=/usr/local/bin/windres READTAPI=/usr/local/bin/readtapi SIZE=/usr/local/bin/size STRINGS=/usr/local/bin/strings INSTALLNAMETOOL=/usr/local/bin/install_name_tool RANLIB=/usr/local/bin/ranlib LD=/usr/local/bin/clang CC=/usr/local/bin/clang CXX=/usr/local/bin/clang++ FC=/usr/local/bin/flang-new 'CPPFLAGS=-I/usr/local/include -O3 -fno-stack-protector -fno-common -fPIC' 'CFLAGS=-I/usr/local/include -O3 -fno-stack-protector -fno-common -fPIC' 'LDFLAGS=-O3 -fno-stack-protector -fno-common -fPIC -L/usr/local/lib --for-linker -rpath /usr/local/lib'

## --------- ##
## Platform. ##
## --------- ##

hostname = Andrews-Mac-Studio.local
uname -m = arm64
uname -r = 24.5.0
uname -s = Darwin
uname -v = Darwin Kernel Version 24.5.0: Tue Apr 22 19:54:25 PDT 2025; root:xnu-11417.121.6~2/RELEASE_ARM64_T6020

/usr/bin/uname -p = arm
/bin/uname -X     = unknown

/bin/arch              = unknown
/usr/bin/arch -k       = unknown
/usr/convex/getsysinfo = unknown
/usr/bin/hostinfo      = Mach kernel version:
	 Darwin Kernel Version 24.5.0: Tue Apr 22 19:54:25 PDT 2025; root:xnu-11417.121.6~2/RELEASE_ARM64_T6020
Kernel configured for up to 12 processors.
12 processors are physically available.
12 processors are logically available.
Processor type: arm64e (ARM64E)
Processors active: 0 1 2 3 4 5 6 7 8 9 10 11
Primary memory available: 64.00 gigabytes
Default processor set: 699 tasks, 3391 threads, 12 processors
Load average: 1.16, Mach factor: 10.83
/bin/machine           = unknown
/usr/bin/oslevel       = unknown
/bin/universe          = unknown

PATH: /Library/Frameworks/Python.framework/Versions/3.13/bin/
PATH: /Library/Java/JavaVirtualMachines/jdk-24.jdk/Contents/Home/bin/
PATH: /usr/local/bin/
PATH: /System/Cryptexes/App/usr/bin/
PATH: /usr/bin/
PATH: /bin/
PATH: /usr/sbin/
PATH: /sbin/
PATH: /var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin/
PATH: /var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin/
PATH: /var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin/
PATH: /opt/X11/bin/
PATH: //Applications/Topaz Gigapixel AI.app/Contents/Resources/bin/
PATH: /Library/Apple/usr/bin/
PATH: /opt/ant/bin/
PATH: /usr/local/clamav/bin/
PATH: /usr/local/share/dotnet/
PATH: ~/.dotnet/tools/
PATH: /Users/<USER>/emsdk/emsdk/
PATH: /Users/<USER>/emsdk/emsdk/upstream/emscripten/
PATH: /usr/local/go/bin/
PATH: /opt/gradle/gradle-8.13/bin/
PATH: /Library/Java/JavaVirtualMachines/graalvm-jdk-24+36.1/Contents/Home/bin/
PATH: /opt/javacc/bin/
PATH: /opt/maven/bin/
PATH: /Users/<USER>/Library/Python/3.13/bin/
PATH: /Library/Frameworks/Python.framework/Versions/3.13/bin/
PATH: /opt/Qt/6.9.0/macos/bin/
PATH: /opt/Qt/Tools/sdktool/libexec/
PATH: /Users/<USER>/.cargo/bin/


## ----------- ##
## Core tests. ##
## ----------- ##

configure:2731: looking for aux files: config.guess config.sub ltmain.sh ar-lib compile missing install-sh
configure:2744:  trying ./
configure:2773:   ./config.guess found
configure:2773:   ./config.sub found
configure:2773:   ./ltmain.sh found
configure:2773:   ./ar-lib found
configure:2773:   ./compile found
configure:2773:   ./missing found
configure:2755:   ./install-sh found
configure:2903: checking for a BSD-compatible install
configure:2977: result: /usr/local/bin/install -c
configure:2988: checking whether sleep supports fractional seconds
configure:3004: result: yes
configure:3007: checking filesystem timestamp resolution
configure:3142: result: 2
configure:3147: checking whether build environment is sane
configure:3188: result: yes
configure:3359: checking for a race-free mkdir -p
configure:3402: result: /usr/local/bin/mkdir -p
configure:3409: checking for gawk
configure:3430: found /usr/local/bin/gawk
configure:3442: result: gawk
configure:3453: checking whether make sets $(MAKE)
configure:3477: result: yes
configure:3503: checking whether make supports nested variables
configure:3522: result: yes
configure:3536: checking xargs -n works
configure:3552: result: yes
configure:3713: checking for gcc
configure:3746: result: /usr/local/bin/clang
configure:4105: checking for C compiler version
configure:4114: /usr/local/bin/clang --version >&5
clang version 20.1.6
Target: arm64-apple-darwin24.5.0
Thread model: posix
InstalledDir: /usr/local/bin
Build config: +assertions
configure:4125: $? = 0
configure:4114: /usr/local/bin/clang -v >&5
clang version 20.1.6
Target: arm64-apple-darwin24.5.0
Thread model: posix
InstalledDir: /usr/local/bin
Build config: +assertions
configure:4125: $? = 0
configure:4114: /usr/local/bin/clang -V >&5
clang: error: argument to '-V' is missing (expected 1 value)
clang: error: no input files
configure:4125: $? = 1
configure:4114: /usr/local/bin/clang -qversion >&5
clang: error: unknown argument '-qversion'; did you mean '--version'?
clang: error: no input files
configure:4125: $? = 1
configure:4114: /usr/local/bin/clang -version >&5
clang: error: unknown argument '-version'; did you mean '--version'?
clang: error: no input files
configure:4125: $? = 1
configure:4145: checking whether the C compiler works
configure:4167: /usr/local/bin/clang -I/usr/local/include -O3 -fno-stack-protector -fno-common -fPIC -I/usr/local/include -O3 -fno-stack-protector -fno-common -fPIC -O3 -fno-stack-protector -fno-common -fPIC -L/usr/local/lib --for-linker -rpath /usr/local/lib conftest.c  >&5
configure:4171: $? = 0
configure:4222: result: yes
configure:4226: checking for C compiler default output file name
configure:4228: result: a.out
configure:4234: checking for suffix of executables
configure:4241: /usr/local/bin/clang -o conftest -I/usr/local/include -O3 -fno-stack-protector -fno-common -fPIC -I/usr/local/include -O3 -fno-stack-protector -fno-common -fPIC -O3 -fno-stack-protector -fno-common -fPIC -L/usr/local/lib --for-linker -rpath /usr/local/lib conftest.c  >&5
configure:4245: $? = 0
configure:4269: result: 
configure:4293: checking whether we are cross compiling
configure:4301: /usr/local/bin/clang -o conftest -I/usr/local/include -O3 -fno-stack-protector -fno-common -fPIC -I/usr/local/include -O3 -fno-stack-protector -fno-common -fPIC -O3 -fno-stack-protector -fno-common -fPIC -L/usr/local/lib --for-linker -rpath /usr/local/lib conftest.c  >&5
configure:4305: $? = 0
configure:4312: ./conftest
configure:4316: $? = 0
configure:4331: result: no
configure:4337: checking for suffix of object files
configure:4360: /usr/local/bin/clang -c -I/usr/local/include -O3 -fno-stack-protector -fno-common -fPIC -I/usr/local/include -O3 -fno-stack-protector -fno-common -fPIC conftest.c >&5
configure:4364: $? = 0
configure:4388: result: o
configure:4392: checking whether the compiler supports GNU C
configure:4412: /usr/local/bin/clang -c -I/usr/local/include -O3 -fno-stack-protector -fno-common -fPIC -I/usr/local/include -O3 -fno-stack-protector -fno-common -fPIC conftest.c >&5
configure:4412: $? = 0
configure:4424: result: yes
configure:4435: checking whether /usr/local/bin/clang accepts -g
configure:4456: /usr/local/bin/clang -c -g -I/usr/local/include -O3 -fno-stack-protector -fno-common -fPIC conftest.c >&5
configure:4456: $? = 0
configure:4503: result: yes
configure:4523: checking for /usr/local/bin/clang option to enable C11 features
configure:4538: /usr/local/bin/clang  -c -I/usr/local/include -O3 -fno-stack-protector -fno-common -fPIC -I/usr/local/include -O3 -fno-stack-protector -fno-common -fPIC conftest.c >&5
configure:4538: $? = 0
configure:4557: result: none needed
configure:4681: checking whether /usr/local/bin/clang understands -c and -o together
configure:4704: /usr/local/bin/clang -c conftest.c -o conftest2.o
configure:4707: $? = 0
configure:4704: /usr/local/bin/clang -c conftest.c -o conftest2.o
configure:4707: $? = 0
configure:4720: result: yes
configure:4740: checking whether make supports the include directive
configure:4755: make -f confmf.GNU && cat confinc.out
this is the am__doit target
configure:4758: $? = 0
configure:4777: result: yes (GNU style)
configure:4803: checking dependency style of /usr/local/bin/clang
configure:4916: result: gcc3
configure:5061: checking for C++ compiler version
configure:5070: /usr/local/bin/clang++ --version >&5
clang version 20.1.6
Target: arm64-apple-darwin24.5.0
Thread model: posix
InstalledDir: /usr/local/bin
Build config: +assertions
configure:5081: $? = 0
configure:5070: /usr/local/bin/clang++ -v >&5
clang version 20.1.6
Target: arm64-apple-darwin24.5.0
Thread model: posix
InstalledDir: /usr/local/bin
Build config: +assertions
configure:5081: $? = 0
configure:5070: /usr/local/bin/clang++ -V >&5
clang++: error: argument to '-V' is missing (expected 1 value)
clang++: error: no input files
configure:5081: $? = 1
configure:5070: /usr/local/bin/clang++ -qversion >&5
clang++: error: unknown argument '-qversion'; did you mean '--version'?
clang++: error: no input files
configure:5081: $? = 1
configure:5085: checking whether the compiler supports GNU C++
configure:5105: /usr/local/bin/clang++ -c  -I/usr/local/include -O3 -fno-stack-protector -fno-common -fPIC conftest.cpp >&5
configure:5105: $? = 0
configure:5117: result: yes
configure:5128: checking whether /usr/local/bin/clang++ accepts -g
configure:5149: /usr/local/bin/clang++ -c -g -I/usr/local/include -O3 -fno-stack-protector -fno-common -fPIC conftest.cpp >&5
configure:5149: $? = 0
configure:5196: result: yes
configure:5216: checking for /usr/local/bin/clang++ option to enable C++11 features
configure:5231: /usr/local/bin/clang++  -c -g -O2 -I/usr/local/include -O3 -fno-stack-protector -fno-common -fPIC conftest.cpp >&5
conftest.cpp:177:25: warning: empty parentheses interpreted as a function declaration [-Wvexing-parse]
  177 |   cxx11test::delegate d2();
      |                         ^~
conftest.cpp:177:25: note: remove parentheses to declare a variable
  177 |   cxx11test::delegate d2();
      |                         ^~
1 warning generated.
configure:5231: $? = 0
configure:5250: result: none needed
configure:5321: checking dependency style of /usr/local/bin/clang++
configure:5434: result: gcc3
configure:5566: checking the archiver (/usr/local/bin/ar) interface
configure:5583: /usr/local/bin/clang -c -I/usr/local/include -O3 -fno-stack-protector -fno-common -fPIC -I/usr/local/include -O3 -fno-stack-protector -fno-common -fPIC conftest.c >&5
configure:5583: $? = 0
configure:5586: /usr/local/bin/ar cr libconftest.a conftest.o >&5
configure:5589: $? = 0
configure:5618: result: ar
configure:5670: checking build system type
configure:5686: result: aarch64-apple-darwin24.5.0
configure:5706: checking host system type
configure:5721: result: aarch64-apple-darwin24.5.0
configure:5762: checking how to print strings
configure:5789: result: printf
configure:5810: checking for a sed that does not truncate output
configure:5882: result: /usr/local/bin/sed
configure:5900: checking for grep that handles long lines and -e
configure:5966: result: /usr/local/bin/grep
configure:5971: checking for egrep
configure:6041: result: /usr/local/bin/grep -E
configure:6048: checking for fgrep
configure:6118: result: /usr/local/bin/grep -F
configure:6155: checking for ld used by /usr/local/bin/clang
configure:6224: result: /usr/local/bin/clang
configure:6231: checking if the linker (/usr/local/bin/clang) is GNU ld
configure:6248: result: no
configure:6260: checking for BSD- or MS-compatible name lister (nm)
configure:6316: result: /usr/local/bin/nm
configure:6458: checking the name lister (/usr/local/bin/nm) interface
configure:6466: /usr/local/bin/clang -c -I/usr/local/include -O3 -fno-stack-protector -fno-common -fPIC -I/usr/local/include -O3 -fno-stack-protector -fno-common -fPIC conftest.c >&5
configure:6469: /usr/local/bin/nm "conftest.o"
configure:6472: output
0000000000000000 S _some_variable
0000000000000000 t ltmp0
0000000000000000 s ltmp1
configure:6480: result: BSD nm
configure:6483: checking whether ln -s works
configure:6487: result: yes
configure:6495: checking the maximum length of command line arguments
configure:6628: result: 786432
configure:6676: checking how to convert aarch64-apple-darwin24.5.0 file names to aarch64-apple-darwin24.5.0 format
configure:6718: result: func_convert_file_noop
configure:6725: checking how to convert aarch64-apple-darwin24.5.0 file names to toolchain format
configure:6747: result: func_convert_file_noop
configure:6754: checking for /usr/local/bin/clang option to reload object files
configure:6763: result: -r
configure:6796: checking for file
configure:6817: found /usr/bin/file
configure:6830: result: file
configure:6893: checking for objdump
configure:6926: result: /usr/local/bin/objdump
configure:6958: checking how to recognize dependent libraries
configure:7167: result: pass_all
configure:7258: checking for dlltool
configure:7279: found /usr/local/bin/dlltool
configure:7291: result: dlltool
configure:7324: checking how to associate runtime and link libraries
configure:7353: result: printf %s\n
configure:7413: checking for ranlib
configure:7446: result: /usr/local/bin/ranlib
configure:7609: checking for archiver @FILE support
configure:7627: /usr/local/bin/clang -c -I/usr/local/include -O3 -fno-stack-protector -fno-common -fPIC -I/usr/local/include -O3 -fno-stack-protector -fno-common -fPIC conftest.c >&5
configure:7627: $? = 0
configure:7631: /usr/local/bin/ar cr libconftest.a @conftest.lst >&5
configure:7634: $? = 0
configure:7639: /usr/local/bin/ar cr libconftest.a @conftest.lst >&5
/usr/local/bin/ar: error: conftest.o: No such file or directory
configure:7642: $? = 1
configure:7655: result: @
configure:7719: checking for strip
configure:7752: result: /usr/local/bin/strip
configure:7855: checking command to parse /usr/local/bin/nm output from /usr/local/bin/clang object
configure:8009: /usr/local/bin/clang -c -I/usr/local/include -O3 -fno-stack-protector -fno-common -fPIC -I/usr/local/include -O3 -fno-stack-protector -fno-common -fPIC conftest.c >&5
configure:8012: $? = 0
configure:8016: /usr/local/bin/nm conftest.o | /usr/local/bin/sed -n -e 's/^.*[	 ]\([ABCDGIRSTW][ABCDGIRSTW]*\)[	 ][	 ]*\([_A-Za-z][_A-Za-z0-9]*\)$/\1 \2 \2/p' | /usr/local/bin/sed '/ __gnu_lto/d' > conftest.nm
cannot find nm_test_var in conftest.nm
configure:8009: /usr/local/bin/clang -c -I/usr/local/include -O3 -fno-stack-protector -fno-common -fPIC -I/usr/local/include -O3 -fno-stack-protector -fno-common -fPIC conftest.c >&5
configure:8012: $? = 0
configure:8016: /usr/local/bin/nm conftest.o | /usr/local/bin/sed -n -e 's/^.*[	 ]\([ABCDGIRSTW][ABCDGIRSTW]*\)[	 ][	 ]*_\([_A-Za-z][_A-Za-z0-9]*\)$/\1 _\2 \2/p' | /usr/local/bin/sed '/ __gnu_lto/d' > conftest.nm
configure:8082: /usr/local/bin/clang -o conftest -I/usr/local/include -O3 -fno-stack-protector -fno-common -fPIC -I/usr/local/include -O3 -fno-stack-protector -fno-common -fPIC -O3 -fno-stack-protector -fno-common -fPIC -L/usr/local/lib --for-linker -rpath /usr/local/lib conftest.c conftstm.o >&5
configure:8085: $? = 0
configure:8124: result: ok
configure:8171: checking for sysroot
configure:8205: result: no
configure:8212: checking for a working dd
configure:8257: result: /usr/local/bin/dd
configure:8261: checking how to truncate binary pipes
configure:8278: result: /usr/local/bin/dd bs=4096 count=1
configure:8618: checking for mt
configure:8654: result: no
configure:8674: checking if : is a manifest tool
configure:8681: : '-?'
configure:8690: result: no
configure:8752: checking for dsymutil
configure:8773: found /usr/local/bin/dsymutil
configure:8785: result: dsymutil
configure:8856: checking for nmedit
configure:8877: found /usr/bin/nmedit
configure:8889: result: nmedit
configure:8960: checking for lipo
configure:8993: result: /usr/local/bin/lipo
configure:9064: checking for otool
configure:9097: result: /usr/local/bin/otool
configure:9168: checking for otool64
configure:9204: result: no
configure:9249: checking for -single_module linker flag
/usr/local/bin/clang -I/usr/local/include -O3 -fno-stack-protector -fno-common -fPIC -O3 -fno-stack-protector -fno-common -fPIC -L/usr/local/lib --for-linker -rpath /usr/local/lib -o libconftest.dylib -dynamiclib -Wl,-single_module conftest.c
ld: warning: -single_module is obsolete
configure:9284: result: no
configure:9289: checking for -no_fixup_chains linker flag
configure:9308: /usr/local/bin/clang -o conftest -I/usr/local/include -O3 -fno-stack-protector -fno-common -fPIC -I/usr/local/include -O3 -fno-stack-protector -fno-common -fPIC -O3 -fno-stack-protector -fno-common -fPIC -L/usr/local/lib --for-linker -rpath /usr/local/lib -Wl,-no_fixup_chains conftest.c  >&5
configure:9308: $? = 0
configure:9323: result: yes
configure:9326: checking for -exported_symbols_list linker flag
configure:9347: /usr/local/bin/clang -o conftest -I/usr/local/include -O3 -fno-stack-protector -fno-common -fPIC -I/usr/local/include -O3 -fno-stack-protector -fno-common -fPIC -O3 -fno-stack-protector -fno-common -fPIC -L/usr/local/lib --for-linker -rpath /usr/local/lib -Wl,-exported_symbols_list,conftest.sym conftest.c  >&5
configure:9347: $? = 0
configure:9360: result: yes
configure:9363: checking for -force_load linker flag
/usr/local/bin/clang -I/usr/local/include -O3 -fno-stack-protector -fno-common -fPIC -c -o conftest.o conftest.c
/usr/local/bin/ar cr libconftest.a conftest.o
/usr/local/bin/ranlib libconftest.a
/usr/local/bin/clang -I/usr/local/include -O3 -fno-stack-protector -fno-common -fPIC -O3 -fno-stack-protector -fno-common -fPIC -L/usr/local/lib --for-linker -rpath /usr/local/lib -o conftest conftest.c -Wl,-force_load,./libconftest.a
configure:9397: result: yes
configure:9487: checking for stdio.h
configure:9487: /usr/local/bin/clang -c -I/usr/local/include -O3 -fno-stack-protector -fno-common -fPIC -I/usr/local/include -O3 -fno-stack-protector -fno-common -fPIC conftest.c >&5
configure:9487: $? = 0
configure:9487: result: yes
configure:9487: checking for stdlib.h
configure:9487: /usr/local/bin/clang -c -I/usr/local/include -O3 -fno-stack-protector -fno-common -fPIC -I/usr/local/include -O3 -fno-stack-protector -fno-common -fPIC conftest.c >&5
configure:9487: $? = 0
configure:9487: result: yes
configure:9487: checking for string.h
configure:9487: /usr/local/bin/clang -c -I/usr/local/include -O3 -fno-stack-protector -fno-common -fPIC -I/usr/local/include -O3 -fno-stack-protector -fno-common -fPIC conftest.c >&5
configure:9487: $? = 0
configure:9487: result: yes
configure:9487: checking for inttypes.h
configure:9487: /usr/local/bin/clang -c -I/usr/local/include -O3 -fno-stack-protector -fno-common -fPIC -I/usr/local/include -O3 -fno-stack-protector -fno-common -fPIC conftest.c >&5
configure:9487: $? = 0
configure:9487: result: yes
configure:9487: checking for stdint.h
configure:9487: /usr/local/bin/clang -c -I/usr/local/include -O3 -fno-stack-protector -fno-common -fPIC -I/usr/local/include -O3 -fno-stack-protector -fno-common -fPIC conftest.c >&5
configure:9487: $? = 0
configure:9487: result: yes
configure:9487: checking for strings.h
configure:9487: /usr/local/bin/clang -c -I/usr/local/include -O3 -fno-stack-protector -fno-common -fPIC -I/usr/local/include -O3 -fno-stack-protector -fno-common -fPIC conftest.c >&5
configure:9487: $? = 0
configure:9487: result: yes
configure:9487: checking for sys/stat.h
configure:9487: /usr/local/bin/clang -c -I/usr/local/include -O3 -fno-stack-protector -fno-common -fPIC -I/usr/local/include -O3 -fno-stack-protector -fno-common -fPIC conftest.c >&5
configure:9487: $? = 0
configure:9487: result: yes
configure:9487: checking for sys/types.h
configure:9487: /usr/local/bin/clang -c -I/usr/local/include -O3 -fno-stack-protector -fno-common -fPIC -I/usr/local/include -O3 -fno-stack-protector -fno-common -fPIC conftest.c >&5
configure:9487: $? = 0
configure:9487: result: yes
configure:9487: checking for unistd.h
configure:9487: /usr/local/bin/clang -c -I/usr/local/include -O3 -fno-stack-protector -fno-common -fPIC -I/usr/local/include -O3 -fno-stack-protector -fno-common -fPIC conftest.c >&5
configure:9487: $? = 0
configure:9487: result: yes
configure:9512: checking for dlfcn.h
configure:9512: /usr/local/bin/clang -c -I/usr/local/include -O3 -fno-stack-protector -fno-common -fPIC -I/usr/local/include -O3 -fno-stack-protector -fno-common -fPIC conftest.c >&5
configure:9512: $? = 0
configure:9512: result: yes
configure:9826: checking for objdir
configure:9843: result: .libs
configure:10109: checking if /usr/local/bin/clang supports -fno-rtti -fno-exceptions
configure:10128: /usr/local/bin/clang -c -I/usr/local/include -O3 -fno-stack-protector -fno-common -fPIC -I/usr/local/include -O3 -fno-stack-protector -fno-common -fPIC -fno-rtti -fno-exceptions conftest.c >&5
configure:10132: $? = 0
configure:10146: result: yes
configure:10519: checking for /usr/local/bin/clang option to produce PIC
configure:10528: result: -fno-common -DPIC
configure:10536: checking if /usr/local/bin/clang PIC flag -fno-common -DPIC works
configure:10555: /usr/local/bin/clang -c -I/usr/local/include -O3 -fno-stack-protector -fno-common -fPIC -I/usr/local/include -O3 -fno-stack-protector -fno-common -fPIC -fno-common -DPIC -DPIC conftest.c >&5
configure:10559: $? = 0
configure:10573: result: yes
configure:10602: checking if /usr/local/bin/clang static flag -static works
configure:10632: result: no
configure:10647: checking if /usr/local/bin/clang supports -c -o file.o
configure:10669: /usr/local/bin/clang -c -I/usr/local/include -O3 -fno-stack-protector -fno-common -fPIC -I/usr/local/include -O3 -fno-stack-protector -fno-common -fPIC -o out/conftest2.o conftest.c >&5
configure:10673: $? = 0
configure:10696: result: yes
configure:10704: checking if /usr/local/bin/clang supports -c -o file.o
configure:10753: result: yes
configure:10786: checking whether the /usr/local/bin/clang linker (/usr/local/bin/clang) supports shared libraries
configure:12070: result: yes
configure:12312: checking dynamic linker characteristics
configure:13696: result: darwin24.5.0 dyld
configure:13818: checking how to hardcode library paths into programs
configure:13843: result: immediate
configure:14443: checking whether stripping libraries is possible
configure:14452: result: yes
configure:14494: checking if libtool supports shared libraries
configure:14496: result: yes
configure:14499: checking whether to build shared libraries
configure:14524: result: yes
configure:14527: checking whether to build static libraries
configure:14531: result: yes
configure:14554: checking how to run the C++ preprocessor
configure:14576: /usr/local/bin/clang++ -E -I/usr/local/include -O3 -fno-stack-protector -fno-common -fPIC conftest.cpp
configure:14576: $? = 0
configure:14592: /usr/local/bin/clang++ -E -I/usr/local/include -O3 -fno-stack-protector -fno-common -fPIC conftest.cpp
conftest.cpp:23:10: fatal error: 'ac_nonexistent.h' file not found
   23 | #include <ac_nonexistent.h>
      |          ^~~~~~~~~~~~~~~~~~
1 error generated.
configure:14592: $? = 1
configure: failed program was:
| /* confdefs.h */
| #define PACKAGE_NAME "libnoodlenet"
| #define PACKAGE_TARNAME "libnoodlenet"
| #define PACKAGE_VERSION "1.0"
| #define PACKAGE_STRING "libnoodlenet 1.0"
| #define PACKAGE_BUGREPORT "<EMAIL>"
| #define PACKAGE_URL ""
| #define PACKAGE "libnoodlenet"
| #define VERSION "1.0"
| #define HAVE_STDIO_H 1
| #define HAVE_STDLIB_H 1
| #define HAVE_STRING_H 1
| #define HAVE_INTTYPES_H 1
| #define HAVE_STDINT_H 1
| #define HAVE_STRINGS_H 1
| #define HAVE_SYS_STAT_H 1
| #define HAVE_SYS_TYPES_H 1
| #define HAVE_UNISTD_H 1
| #define STDC_HEADERS 1
| #define HAVE_DLFCN_H 1
| #define LT_OBJDIR ".libs/"
| /* end confdefs.h.  */
| #include <ac_nonexistent.h>
configure:14621: result: /usr/local/bin/clang++ -E
configure:14635: /usr/local/bin/clang++ -E -I/usr/local/include -O3 -fno-stack-protector -fno-common -fPIC conftest.cpp
configure:14635: $? = 0
configure:14651: /usr/local/bin/clang++ -E -I/usr/local/include -O3 -fno-stack-protector -fno-common -fPIC conftest.cpp
conftest.cpp:23:10: fatal error: 'ac_nonexistent.h' file not found
   23 | #include <ac_nonexistent.h>
      |          ^~~~~~~~~~~~~~~~~~
1 error generated.
configure:14651: $? = 1
configure: failed program was:
| /* confdefs.h */
| #define PACKAGE_NAME "libnoodlenet"
| #define PACKAGE_TARNAME "libnoodlenet"
| #define PACKAGE_VERSION "1.0"
| #define PACKAGE_STRING "libnoodlenet 1.0"
| #define PACKAGE_BUGREPORT "<EMAIL>"
| #define PACKAGE_URL ""
| #define PACKAGE "libnoodlenet"
| #define VERSION "1.0"
| #define HAVE_STDIO_H 1
| #define HAVE_STDLIB_H 1
| #define HAVE_STRING_H 1
| #define HAVE_INTTYPES_H 1
| #define HAVE_STDINT_H 1
| #define HAVE_STRINGS_H 1
| #define HAVE_SYS_STAT_H 1
| #define HAVE_SYS_TYPES_H 1
| #define HAVE_UNISTD_H 1
| #define STDC_HEADERS 1
| #define HAVE_DLFCN_H 1
| #define LT_OBJDIR ".libs/"
| /* end confdefs.h.  */
| #include <ac_nonexistent.h>
configure:14819: checking for ld used by /usr/local/bin/clang++
configure:14888: result: /usr/local/bin/clang
configure:14895: checking if the linker (/usr/local/bin/clang) is GNU ld
configure:14912: result: no
configure:14966: checking whether the /usr/local/bin/clang++ linker (/usr/local/bin/clang) supports shared libraries
configure:16054: result: yes
configure:16090: /usr/local/bin/clang++ -c -g -O2 -I/usr/local/include -O3 -fno-stack-protector -fno-common -fPIC conftest.cpp >&5
configure:16093: $? = 0
configure:16579: checking for /usr/local/bin/clang++ option to produce PIC
configure:16588: result: -fno-common -DPIC
configure:16596: checking if /usr/local/bin/clang++ PIC flag -fno-common -DPIC works
configure:16615: /usr/local/bin/clang++ -c -g -O2 -I/usr/local/include -O3 -fno-stack-protector -fno-common -fPIC -fno-common -DPIC -DPIC conftest.cpp >&5
configure:16619: $? = 0
configure:16633: result: yes
configure:16656: checking if /usr/local/bin/clang++ static flag -static works
configure:16686: result: no
configure:16698: checking if /usr/local/bin/clang++ supports -c -o file.o
configure:16720: /usr/local/bin/clang++ -c -g -O2 -I/usr/local/include -O3 -fno-stack-protector -fno-common -fPIC -o out/conftest2.o conftest.cpp >&5
configure:16724: $? = 0
configure:16747: result: yes
configure:16752: checking if /usr/local/bin/clang++ supports -c -o file.o
configure:16801: result: yes
configure:16831: checking whether the /usr/local/bin/clang++ linker (/usr/local/bin/clang) supports shared libraries
configure:16871: result: yes
configure:17014: checking dynamic linker characteristics
configure:18308: result: darwin24.5.0 dyld
configure:18373: checking how to hardcode library paths into programs
configure:18398: result: immediate
configure:18464: checking for stdio.h
configure:18464: result: yes
configure:18470: checking for stdlib.h
configure:18470: result: yes
configure:18476: checking for string.h
configure:18476: result: yes
configure:18482: checking for math.h
configure:18482: /usr/local/bin/clang -c -I/usr/local/include -O3 -fno-stack-protector -fno-common -fPIC -I/usr/local/include -O3 -fno-stack-protector -fno-common -fPIC conftest.c >&5
configure:18482: $? = 0
configure:18482: result: yes
configure:18603: checking that generated files are newer than configure
configure:18609: result: done
configure:18648: creating ./config.status

## ---------------------- ##
## Running config.status. ##
## ---------------------- ##

This file was extended by libnoodlenet config.status 1.0, which was
generated by GNU Autoconf 2.72.  Invocation command line was

  CONFIG_FILES    = 
  CONFIG_HEADERS  = 
  CONFIG_LINKS    = 
  CONFIG_COMMANDS = 
  $ ./config.status 

on Andrews-Mac-Studio.local

config.status:1152: creating Makefile
config.status:1152: creating libnoodlenet.pc
config.status:1152: creating config.h
config.status:1333: config.h is unchanged
config.status:1381: executing depfiles commands
config.status:1458: cd .       && sed -e '/# am--include-marker/d' Makefile         | make -f - am--depfiles
make: Nothing to be done for `am--depfiles'.
config.status:1463: $? = 0
config.status:1381: executing libtool commands

## ---------------- ##
## Cache variables. ##
## ---------------- ##

ac_cv_build=aarch64-apple-darwin24.5.0
ac_cv_c_compiler_gnu=yes
ac_cv_cxx_compiler_gnu=yes
ac_cv_env_CCC_set=
ac_cv_env_CCC_value=
ac_cv_env_CC_set=set
ac_cv_env_CC_value=/usr/local/bin/clang
ac_cv_env_CFLAGS_set=set
ac_cv_env_CFLAGS_value='-I/usr/local/include -O3 -fno-stack-protector -fno-common -fPIC'
ac_cv_env_CPPFLAGS_set=set
ac_cv_env_CPPFLAGS_value='-I/usr/local/include -O3 -fno-stack-protector -fno-common -fPIC'
ac_cv_env_CXXCPP_set=
ac_cv_env_CXXCPP_value=
ac_cv_env_CXXFLAGS_set=
ac_cv_env_CXXFLAGS_value=
ac_cv_env_CXX_set=set
ac_cv_env_CXX_value=/usr/local/bin/clang++
ac_cv_env_LDFLAGS_set=set
ac_cv_env_LDFLAGS_value='-O3 -fno-stack-protector -fno-common -fPIC -L/usr/local/lib --for-linker -rpath /usr/local/lib'
ac_cv_env_LIBS_set=
ac_cv_env_LIBS_value=
ac_cv_env_LT_SYS_LIBRARY_PATH_set=
ac_cv_env_LT_SYS_LIBRARY_PATH_value=
ac_cv_env_build_alias_set=
ac_cv_env_build_alias_value=
ac_cv_env_host_alias_set=
ac_cv_env_host_alias_value=
ac_cv_env_target_alias_set=
ac_cv_env_target_alias_value=
ac_cv_header_dlfcn_h=yes
ac_cv_header_inttypes_h=yes
ac_cv_header_math_h=yes
ac_cv_header_stdint_h=yes
ac_cv_header_stdio_h=yes
ac_cv_header_stdlib_h=yes
ac_cv_header_string_h=yes
ac_cv_header_strings_h=yes
ac_cv_header_sys_stat_h=yes
ac_cv_header_sys_types_h=yes
ac_cv_header_unistd_h=yes
ac_cv_host=aarch64-apple-darwin24.5.0
ac_cv_objext=o
ac_cv_path_EGREP='/usr/local/bin/grep -E'
ac_cv_path_EGREP_TRADITIONAL='/usr/local/bin/grep -E'
ac_cv_path_FGREP='/usr/local/bin/grep -F'
ac_cv_path_GREP=/usr/local/bin/grep
ac_cv_path_SED=/usr/local/bin/sed
ac_cv_path_install='/usr/local/bin/install -c'
ac_cv_path_lt_DD=/usr/local/bin/dd
ac_cv_path_mkdir=/usr/local/bin/mkdir
ac_cv_prog_AWK=gawk
ac_cv_prog_CXXCPP='/usr/local/bin/clang++ -E'
ac_cv_prog_FILECMD=file
ac_cv_prog_ac_ct_CC=/usr/local/bin/clang
ac_cv_prog_ac_ct_DLLTOOL=dlltool
ac_cv_prog_ac_ct_DSYMUTIL=dsymutil
ac_cv_prog_ac_ct_LIPO=/usr/local/bin/lipo
ac_cv_prog_ac_ct_NMEDIT=nmedit
ac_cv_prog_ac_ct_OBJDUMP=/usr/local/bin/objdump
ac_cv_prog_ac_ct_OTOOL=/usr/local/bin/otool
ac_cv_prog_ac_ct_RANLIB=/usr/local/bin/ranlib
ac_cv_prog_ac_ct_STRIP=/usr/local/bin/strip
ac_cv_prog_cc_c11=
ac_cv_prog_cc_g=yes
ac_cv_prog_cc_stdc=
ac_cv_prog_cxx_cxx11=
ac_cv_prog_cxx_g=yes
ac_cv_prog_cxx_stdcxx=
ac_cv_prog_make_make_set=yes
am_cv_CC_dependencies_compiler_type=gcc3
am_cv_CXX_dependencies_compiler_type=gcc3
am_cv_ar_interface=ar
am_cv_filesystem_timestamp_resolution=2
am_cv_make_support_nested_variables=yes
am_cv_prog_cc_c_o=yes
am_cv_sleep_fractional_seconds=yes
am_cv_xargs_n_works=yes
lt_cv_apple_cc_single_mod=no
lt_cv_ar_at_file=@
lt_cv_deplibs_check_method=pass_all
lt_cv_file_magic_cmd='$MAGIC_CMD'
lt_cv_file_magic_test_file=
lt_cv_ld_exported_symbols_list=yes
lt_cv_ld_force_load=yes
lt_cv_ld_reload_flag=-r
lt_cv_nm_interface='BSD nm'
lt_cv_objdir=.libs
lt_cv_path_LD=/usr/local/bin/clang
lt_cv_path_LDCXX=/usr/local/bin/clang
lt_cv_path_NM=/usr/local/bin/nm
lt_cv_path_manifest_tool=no
lt_cv_prog_compiler_c_o=yes
lt_cv_prog_compiler_c_o_CXX=yes
lt_cv_prog_compiler_pic='-fno-common -DPIC'
lt_cv_prog_compiler_pic_CXX='-fno-common -DPIC'
lt_cv_prog_compiler_pic_works=yes
lt_cv_prog_compiler_pic_works_CXX=yes
lt_cv_prog_compiler_rtti_exceptions=yes
lt_cv_prog_compiler_static_works=no
lt_cv_prog_compiler_static_works_CXX=no
lt_cv_prog_gnu_ld=no
lt_cv_prog_gnu_ldcxx=no
lt_cv_sharedlib_from_linklib_cmd='printf %s\n'
lt_cv_support_no_fixup_chains=yes
lt_cv_sys_global_symbol_pipe='/usr/local/bin/sed -n -e '\''s/^.*[	 ]\([ABCDGIRSTW][ABCDGIRSTW]*\)[	 ][	 ]*_\([_A-Za-z][_A-Za-z0-9]*\)$/\1 _\2 \2/p'\'' | /usr/local/bin/sed '\''/ __gnu_lto/d'\'''
lt_cv_sys_global_symbol_to_c_name_address='/usr/local/bin/sed -n -e '\''s/^: \(.*\) .*$/  {"\1", (void *) 0},/p'\'' -e '\''s/^[ABCDGIRSTW][ABCDGIRSTW]* .* \(.*\)$/  {"\1", (void *) \&\1},/p'\'''
lt_cv_sys_global_symbol_to_c_name_address_lib_prefix='/usr/local/bin/sed -n -e '\''s/^: \(.*\) .*$/  {"\1", (void *) 0},/p'\'' -e '\''s/^[ABCDGIRSTW][ABCDGIRSTW]* .* \(lib.*\)$/  {"\1", (void *) \&\1},/p'\'' -e '\''s/^[ABCDGIRSTW][ABCDGIRSTW]* .* \(.*\)$/  {"lib\1", (void *) \&\1},/p'\'''
lt_cv_sys_global_symbol_to_cdecl='/usr/local/bin/sed -n -e '\''s/^T .* \(.*\)$/extern int \1();/p'\'' -e '\''s/^[ABCDGIRSTW][ABCDGIRSTW]* .* \(.*\)$/extern char \1;/p'\'''
lt_cv_sys_global_symbol_to_import=
lt_cv_sys_max_cmd_len=786432
lt_cv_to_host_file_cmd=func_convert_file_noop
lt_cv_to_tool_file_cmd=func_convert_file_noop
lt_cv_truncate_bin='/usr/local/bin/dd bs=4096 count=1'

## ----------------- ##
## Output variables. ##
## ----------------- ##

ACLOCAL='${SHELL} '\''/Users/<USER>/Desktop/projects/sensuser/repo/libnoodlenet/missing'\'' aclocal-1.17'
AMDEPBACKSLASH='\'
AMDEP_FALSE='#'
AMDEP_TRUE=''
AMTAR='$${TAR-tar}'
AM_BACKSLASH='\'
AM_DEFAULT_V='$(AM_DEFAULT_VERBOSITY)'
AM_DEFAULT_VERBOSITY='1'
AM_V='$(V)'
AR='/usr/local/bin/ar'
AUTOCONF='${SHELL} '\''/Users/<USER>/Desktop/projects/sensuser/repo/libnoodlenet/missing'\'' autoconf'
AUTOHEADER='${SHELL} '\''/Users/<USER>/Desktop/projects/sensuser/repo/libnoodlenet/missing'\'' autoheader'
AUTOMAKE='${SHELL} '\''/Users/<USER>/Desktop/projects/sensuser/repo/libnoodlenet/missing'\'' automake-1.17'
AWK='gawk'
CC='/usr/local/bin/clang'
CCDEPMODE='depmode=gcc3'
CFLAGS='-I/usr/local/include -O3 -fno-stack-protector -fno-common -fPIC'
CPPFLAGS='-I/usr/local/include -O3 -fno-stack-protector -fno-common -fPIC'
CSCOPE='cscope'
CTAGS='ctags'
CXX='/usr/local/bin/clang++'
CXXCPP='/usr/local/bin/clang++ -E'
CXXDEPMODE='depmode=gcc3'
CXXFLAGS='-g -O2'
CYGPATH_W='echo'
DEFS='-DHAVE_CONFIG_H'
DEPDIR='.deps'
DLLTOOL='dlltool'
DSYMUTIL='dsymutil'
DUMPBIN=''
ECHO_C='\c'
ECHO_N=''
ECHO_T=''
EGREP='/usr/local/bin/grep -E'
ETAGS='etags'
EXEEXT=''
FGREP='/usr/local/bin/grep -F'
FILECMD='file'
GREP='/usr/local/bin/grep'
INSTALL_DATA='${INSTALL} -m 644'
INSTALL_PROGRAM='${INSTALL}'
INSTALL_SCRIPT='${INSTALL}'
INSTALL_STRIP_PROGRAM='$(install_sh) -c -s'
LD='/usr/local/bin/clang'
LDFLAGS='-O3 -fno-stack-protector -fno-common -fPIC -L/usr/local/lib --for-linker -rpath /usr/local/lib'
LIBOBJS=''
LIBS=''
LIBTOOL='$(SHELL) $(top_builddir)/libtool'
LIPO='/usr/local/bin/lipo'
LN_S='ln -s'
LTLIBOBJS=''
LT_SYS_LIBRARY_PATH=''
MAKEINFO='${SHELL} '\''/Users/<USER>/Desktop/projects/sensuser/repo/libnoodlenet/missing'\'' makeinfo'
MANIFEST_TOOL=':'
MKDIR_P='/usr/local/bin/mkdir -p'
NM='/usr/local/bin/nm'
NMEDIT='nmedit'
OBJDUMP='/usr/local/bin/objdump'
OBJEXT='o'
OTOOL64=':'
OTOOL='/usr/local/bin/otool'
PACKAGE='libnoodlenet'
PACKAGE_BUGREPORT='<EMAIL>'
PACKAGE_NAME='libnoodlenet'
PACKAGE_STRING='libnoodlenet 1.0'
PACKAGE_TARNAME='libnoodlenet'
PACKAGE_URL=''
PACKAGE_VERSION='1.0'
PATH_SEPARATOR=':'
RANLIB='/usr/local/bin/ranlib'
SED='/usr/local/bin/sed'
SET_MAKE=''
SHELL='/bin/sh'
STRIP='/usr/local/bin/strip'
VERSION='1.0'
ac_ct_AR=''
ac_ct_CC='/usr/local/bin/clang'
ac_ct_CXX=''
ac_ct_DUMPBIN=''
am__EXEEXT_FALSE=''
am__EXEEXT_TRUE='#'
am__fastdepCC_FALSE='#'
am__fastdepCC_TRUE=''
am__fastdepCXX_FALSE='#'
am__fastdepCXX_TRUE=''
am__include='include'
am__isrc=''
am__leading_dot='.'
am__nodep='_no'
am__quote=''
am__rm_f_notfound=''
am__tar='$${TAR-tar} chof - "$$tardir"'
am__untar='$${TAR-tar} xf -'
am__xargs_n='xargs -n'
bindir='${exec_prefix}/bin'
build='aarch64-apple-darwin24.5.0'
build_alias=''
build_cpu='aarch64'
build_os='darwin24.5.0'
build_vendor='apple'
datadir='${datarootdir}'
datarootdir='${prefix}/share'
docdir='${datarootdir}/doc/${PACKAGE_TARNAME}'
dvidir='${docdir}'
exec_prefix='${prefix}'
host='aarch64-apple-darwin24.5.0'
host_alias=''
host_cpu='aarch64'
host_os='darwin24.5.0'
host_vendor='apple'
htmldir='${docdir}'
includedir='${prefix}/include'
infodir='${datarootdir}/info'
install_sh='${SHELL} /Users/<USER>/Desktop/projects/sensuser/repo/libnoodlenet/install-sh'
libdir='${exec_prefix}/lib'
libexecdir='${exec_prefix}/libexec'
localedir='${datarootdir}/locale'
localstatedir='${prefix}/var'
mandir='${datarootdir}/man'
mkdir_p='$(MKDIR_P)'
oldincludedir='/usr/include'
pdfdir='${docdir}'
prefix='/usr/local'
program_transform_name='s,x,x,'
psdir='${docdir}'
runstatedir='${localstatedir}/run'
sbindir='${exec_prefix}/sbin'
sharedstatedir='${prefix}/com'
sysconfdir='${prefix}/etc'
target_alias=''

## ----------- ##
## confdefs.h. ##
## ----------- ##

/* confdefs.h */
#define PACKAGE_NAME "libnoodlenet"
#define PACKAGE_TARNAME "libnoodlenet"
#define PACKAGE_VERSION "1.0"
#define PACKAGE_STRING "libnoodlenet 1.0"
#define PACKAGE_BUGREPORT "<EMAIL>"
#define PACKAGE_URL ""
#define PACKAGE "libnoodlenet"
#define VERSION "1.0"
#define HAVE_STDIO_H 1
#define HAVE_STDLIB_H 1
#define HAVE_STRING_H 1
#define HAVE_INTTYPES_H 1
#define HAVE_STDINT_H 1
#define HAVE_STRINGS_H 1
#define HAVE_SYS_STAT_H 1
#define HAVE_SYS_TYPES_H 1
#define HAVE_UNISTD_H 1
#define STDC_HEADERS 1
#define HAVE_DLFCN_H 1
#define LT_OBJDIR ".libs/"
#define HAVE_STDIO_H 1
#define HAVE_STDLIB_H 1
#define HAVE_STRING_H 1
#define HAVE_MATH_H 1

configure: exit 0
