<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MainWindow</class>
 <widget class="QMainWindow" name="MainWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1024</width>
    <height>768</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>MLP Image Classifier</string>
  </property>
  <widget class="QWidget" name="centralwidget">
   <layout class="QVBoxLayout" name="verticalLayout">
    <item>
     <widget class="QTabWidget" name="tabWidget">
      <property name="currentIndex">
       <number>0</number>
      </property>
      <widget class="QWidget" name="tabSetupTraining">
       <attribute name="title">
        <string>Setup &amp; Training</string>
       </attribute>
       <layout class="QVBoxLayout" name="verticalLayout_2">
        <item>
         <layout class="QHBoxLayout" name="horizontalLayout">
          <item>
           <layout class="QVBoxLayout" name="verticalLayout_3">
            <item>
             <widget class="QGroupBox" name="groupBox">
              <property name="title">
               <string>Data Loading</string>
              </property>
              <layout class="QGridLayout" name="gridLayout">
               <item row="0" column="0">
                <widget class="QLabel" name="label">
                 <property name="text">
                  <string>Positive Examples:</string>
                 </property>
                </widget>
               </item>
               <item row="0" column="1">
                <widget class="QLabel" name="lblPositiveDir">
                 <property name="text">
                  <string>Not set</string>
                 </property>
                </widget>
               </item>
               <item row="0" column="2">
                <widget class="QPushButton" name="btnLoadPositive">
                 <property name="text">
                  <string>Load</string>
                 </property>
                </widget>
               </item>
               <item row="1" column="0">
                <widget class="QLabel" name="label_2">
                 <property name="text">
                  <string>Negative Examples:</string>
                 </property>
                </widget>
               </item>
               <item row="1" column="1">
                <widget class="QLabel" name="lblNegativeDir">
                 <property name="text">
                  <string>Not set</string>
                 </property>
                </widget>
               </item>
               <item row="1" column="2">
                <widget class="QPushButton" name="btnLoadNegative">
                 <property name="text">
                  <string>Load</string>
                 </property>
                </widget>
               </item>
              </layout>
             </widget>
            </item>
            <item>
             <widget class="QGroupBox" name="groupBox_2">
              <property name="title">
               <string>Network Configuration</string>
              </property>
              <layout class="QGridLayout" name="gridLayout_2">
               <item row="0" column="0">
                <widget class="QLabel" name="label_3">
                 <property name="text">
                  <string>Hidden Neurons:</string>
                 </property>
                </widget>
               </item>
               <item row="0" column="1">
                <widget class="QSpinBox" name="sbHiddenNeurons">
                 <property name="minimum">
                  <number>1</number>
                 </property>
                 <property name="maximum">
                  <number>1024</number>
                 </property>
                 <property name="value">
                  <number>128</number>
                 </property>
                </widget>
               </item>
               <item row="1" column="0">
                <widget class="QLabel" name="label_4">
                 <property name="text">
                  <string>Hidden Activation:</string>
                 </property>
                </widget>
               </item>
               <item row="1" column="1">
                <widget class="QComboBox" name="cbHiddenActivation"/>
               </item>
               <item row="2" column="0">
                <widget class="QLabel" name="label_5">
                 <property name="text">
                  <string>Bias:</string>
                 </property>
                </widget>
               </item>
               <item row="2" column="1">
                <widget class="QDoubleSpinBox" name="sbBias">
                 <property name="minimum">
                  <double>-10.000000000000000</double>
                 </property>
                 <property name="maximum">
                  <double>10.000000000000000</double>
                 </property>
                 <property name="singleStep">
                  <double>0.100000000000000</double>
                 </property>
                </widget>
               </item>
              </layout>
             </widget>
            </item>
            <item>
             <widget class="QGroupBox" name="groupBox_3">
              <property name="title">
               <string>Training Parameters</string>
              </property>
              <layout class="QGridLayout" name="gridLayout_3">
               <item row="0" column="0">
                <widget class="QLabel" name="label_6">
                 <property name="text">
                  <string>Learning Rate:</string>
                 </property>
                </widget>
               </item>
               <item row="0" column="1">
                <widget class="QDoubleSpinBox" name="sbLearningRate">
                 <property name="decimals">
                  <number>4</number>
                 </property>
                 <property name="minimum">
                  <double>0.000100000000000</double>
                 </property>
                 <property name="maximum">
                  <double>1.000000000000000</double>
                 </property>
                 <property name="singleStep">
                  <double>0.001000000000000</double>
                 </property>
                 <property name="value">
                  <double>0.010000000000000</double>
                 </property>
                </widget>
               </item>
               <item row="1" column="0">
                <widget class="QLabel" name="label_7">
                 <property name="text">
                  <string>Epochs:</string>
                 </property>
                </widget>
               </item>
               <item row="1" column="1">
                <widget class="QSpinBox" name="sbEpochs">
                 <property name="minimum">
                  <number>1</number>
                 </property>
                 <property name="maximum">
                  <number>1000</number>
                 </property>
                 <property name="value">
                  <number>100</number>
                 </property>
                </widget>
               </item>
               <item row="2" column="0">
                <widget class="QLabel" name="label_8">
                 <property name="text">
                  <string>Batch Size:</string>
                 </property>
                </widget>
               </item>
               <item row="2" column="1">
                <widget class="QSpinBox" name="sbBatchSize">
                 <property name="minimum">
                  <number>1</number>
                 </property>
                 <property name="maximum">
                  <number>100</number>
                 </property>
                 <property name="value">
                  <number>10</number>
                 </property>
                </widget>
               </item>
               <item row="3" column="0">
                <widget class="QLabel" name="label_9">
                 <property name="text">
                  <string>Shuffle Data:</string>
                 </property>
                </widget>
               </item>
               <item row="3" column="1">
                <widget class="QCheckBox" name="cbShuffle">
                 <property name="text">
                  <string/>
                 </property>
                 <property name="checked">
                  <bool>true</bool>
                 </property>
                </widget>
               </item>
               <item row="4" column="0">
                <widget class="QLabel" name="label_10">
                 <property name="text">
                  <string>Optimizer:</string>
                 </property>
                </widget>
               </item>
               <item row="4" column="1">
                <widget class="QComboBox" name="cbOptimizer">
                 <property name="toolTip">
                  <string>Selects the optimization algorithm used during training. Adam is recommended as a good default for most problems.</string>
                 </property>
                 <item>
                  <property name="text">
                   <string>SGD (Stochastic Gradient Descent)</string>
                  </property>
                 </item>
                 <item>
                  <property name="text">
                   <string>RMSprop</string>
                  </property>
                 </item>
                 <item>
                  <property name="text">
                   <string>Adam</string>
                  </property>
                 </item>
                </widget>
               </item>
              </layout>
             </widget>
            </item>
            <item>
             <widget class="QGroupBox" name="groupBox_4">
              <property name="title">
               <string>Model Management</string>
              </property>
              <layout class="QHBoxLayout" name="horizontalLayout_3">
               <item>
                <widget class="QPushButton" name="btnExportModel">
                 <property name="text">
                  <string>Export Model</string>
                 </property>
                </widget>
               </item>
               <item>
                <widget class="QPushButton" name="btnImportModel">
                 <property name="text">
                  <string>Import Model</string>
                 </property>
                </widget>
               </item>
              </layout>
             </widget>
            </item>
            <item>
             <spacer name="verticalSpacer">
              <property name="orientation">
               <enum>Qt::Vertical</enum>
              </property>
              <property name="sizeHint" stdset="0">
               <size>
                <width>20</width>
                <height>40</height>
               </size>
              </property>
             </spacer>
            </item>
           </layout>
          </item>
          <item>
           <layout class="QVBoxLayout" name="verticalLayout_4">
            <item>
             <widget class="QGroupBox" name="groupBox_5">
              <property name="title">
               <string>Current Image</string>
              </property>
              <layout class="QVBoxLayout" name="verticalLayout_5">
               <item>
                <widget class="QLabel" name="lblCurrentImage">
                 <property name="minimumSize">
                  <size>
                   <width>512</width>
                   <height>512</height>
                  </size>
                 </property>
                 <property name="text">
                  <string>No image loaded</string>
                 </property>
                 <property name="alignment">
                  <set>Qt::AlignCenter</set>
                 </property>
                </widget>
               </item>
               <item>
                <widget class="QLabel" name="lblImageInfo">
                 <property name="text">
                  <string/>
                 </property>
                </widget>
               </item>
               <item>
                <layout class="QHBoxLayout" name="horizontalLayout_2">
                 <item>
                  <widget class="QPushButton" name="btnPrevImage">
                   <property name="text">
                    <string>Previous</string>
                   </property>
                  </widget>
                 </item>
                 <item>
                  <widget class="QPushButton" name="btnNextImage">
                   <property name="text">
                    <string>Next</string>
                   </property>
                  </widget>
                 </item>
                </layout>
               </item>
               <item>
                <widget class="QLabel" name="lblPrediction">
                 <property name="text">
                  <string>No prediction</string>
                 </property>
                </widget>
               </item>
              </layout>
             </widget>
            </item>
            <item>
             <widget class="QGroupBox" name="groupBox_6">
              <property name="title">
               <string>Training &amp; Evaluation</string>
              </property>
              <layout class="QVBoxLayout" name="verticalLayout_6">
               <item>
                <layout class="QHBoxLayout" name="horizontalLayout_4">
                 <item>
                  <widget class="QPushButton" name="btnTrain">
                   <property name="text">
                    <string>Train Model</string>
                   </property>
                  </widget>
                 </item>
                 <item>
                  <widget class="QPushButton" name="btnEvaluate">
                   <property name="text">
                    <string>Evaluate Model</string>
                   </property>
                  </widget>
                 </item>
                </layout>
               </item>
               <item>
                <widget class="QProgressBar" name="progressBar">
                 <property name="value">
                  <number>0</number>
                 </property>
                </widget>
               </item>
               <item>
                <widget class="QLabel" name="lblAccuracy">
                 <property name="text">
                  <string>No evaluation</string>
                 </property>
                 <property name="alignment">
                  <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignTop</set>
                 </property>
                </widget>
               </item>
              </layout>
             </widget>
            </item>
           </layout>
          </item>
         </layout>
        </item>
       </layout>
      </widget>
      <widget class="QWidget" name="tabInputLayer">
       <attribute name="title">
        <string>Input Layer</string>
       </attribute>
       <layout class="QVBoxLayout" name="verticalLayout_7">
        <item>
         <widget class="QGraphicsView" name="gvInputLayer"/>
        </item>
       </layout>
      </widget>
      <widget class="QWidget" name="tabHiddenLayer">
       <attribute name="title">
        <string>Hidden Layer</string>
       </attribute>
       <layout class="QVBoxLayout" name="verticalLayout_8">
        <item>
         <widget class="QGraphicsView" name="gvHiddenLayer"/>
        </item>
       </layout>
      </widget>
      <widget class="QWidget" name="tabOutputLayer">
       <attribute name="title">
        <string>Output Layer</string>
       </attribute>
       <layout class="QVBoxLayout" name="verticalLayout_9">
        <item>
         <widget class="QGraphicsView" name="gvOutputLayer"/>
        </item>
       </layout>
      </widget>
     </widget>
    </item>
   </layout>
  </widget>
  <widget class="QMenuBar" name="menubar">
   <property name="geometry">
    <rect>
     <x>0</x>
     <y>0</y>
     <width>1024</width>
     <height>24</height>
    </rect>
   </property>
  </widget>
  <widget class="QStatusBar" name="statusbar"/>
 </widget>
 <resources/>
 <connections/>
</ui>
