# libnoodlenet.la - a libtool library file
# Generated by libtool (GNU libtool) 2.5.4
#
# Please DO NOT delete this file!
# It is necessary for linking the library.

# The name that we can dlopen(3).
dlname='libnoodlenet.0.dylib'

# Names of this library.
library_names='libnoodlenet.0.dylib libnoodlenet.dylib'

# The name of the static archive.
old_library='libnoodlenet.a'

# Linker flags that cannot go in dependency_libs.
inherited_linker_flags=' '

# Libraries that this one depends upon.
dependency_libs=' -L/usr/local/lib'

# Names of additional weak libraries provided by this library
weak_library_names=''

# Version information for libnoodlenet.
current=0
age=0
revision=0

# Is this an already installed library?
installed=yes

# Should we warn about portability when linking against -modules?
shouldnotlink=no

# Files to dlopen/dlpreopen
dlopen=''
dlpreopen=''

# Directory that this library needs to be installed in:
libdir='/usr/local/lib'
